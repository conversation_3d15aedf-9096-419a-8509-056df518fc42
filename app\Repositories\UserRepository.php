<?php

namespace App\Repositories;

use App\Models\User;

class UserRepository
{
    public function getUserInfo(User $user)
    {
        $role = $user->roles->first()?->name ?? null;

        if ($role === 'super_admin') {
            return [
                'name' => $user->name,
                'email' => $user->email,
                'profile_image_url' => $user->profile_image_url,
                'last_login_time' => $user->last_login_at ?? null,
            ];
        }

        if ($role === 'client') {
            $client = $user->client;
            return [
                'name' => $user->name,
                'profile_image_url' => $user->profile_image_url,
                'email' => $user->email,
                'phone' => $client?->phone,
                'address' => $client?->address,
                'team_members' => $client?->teamMembers->pluck('user.name')->values() ?? [],
            ];
        }

        // Account Manager or other roles
        $teamMember = $user->teamMember;
        return [
            'name' => $user->name,
            'profile_image_url' => $user->profile_image_url,
            'email' => $user->email,
            'phone' => $teamMember?->phone,
            'role' => $role,
            'work_hours' => [
                'start' => $teamMember?->start_time?->format('H:i'),
                'end' => $teamMember?->end_time?->format('H:i'),
            ],
            'work_days' => $teamMember?->work_days ?? [],
            'last_active' => $teamMember?->last_active_at,
            'last_login_time' => $user->last_login_at ?? null,
            'assigned_clients' => $teamMember?->clients->pluck('name')->values() ?? [],
        ];
    }
}
