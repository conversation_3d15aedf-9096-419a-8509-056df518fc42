<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class TeamMember extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'start_time',
        'end_time',
        'whatsapp_number',
        'chat_disabled',
        'work_days',
    ];

    protected $casts = [
        'chat_disabled' => 'boolean',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'work_days' => 'array',
    ];

    protected $appends = ['last_active_at', 'online'];

    protected $with = ['clients'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class, 'client_team_member')
            ->withTimestamps();
    }
    public function getAssignedClientsAttribute(): array
    {
        return $this->clients()->pluck('clients.id')->toArray();
    }

    public function getLastActiveAtAttribute(): ?string
    {
        $now = Carbon::now();
        $today = strtolower($now->format('l'));
        if (!in_array($today, $this->work_days)) {
            $lastWorkDay = $now->copy();
            $daysChecked = 0;

            while ($daysChecked < 7) {
                $lastWorkDay->subDay();
                if (in_array(strtolower($lastWorkDay->format('l')), $this->work_days)) {
                    $now = $lastWorkDay->copy()->setTimeFrom($this->end_time);
                    break;
                }
                $daysChecked++;
            }

            if ($daysChecked >= 7) {
                return null;
            }

            return Carbon::now()->diffForHumans($now, ['parts' => 1, 'short' => true]);
        }
        $startTime = Carbon::createFromFormat('H:i', $this->start_time->format('H:i'));
        $endTime = Carbon::createFromFormat('H:i', $this->end_time->format('H:i'));

        $startDateTime = $now->copy()->setTimeFrom($startTime);
        $endDateTime = $now->copy()->setTimeFrom($endTime);

        if ($now->between($startDateTime, $endDateTime)) {
            return 'Online now';
        }

        if ($now->lessThan($startDateTime)) {
            return 'Not started yet';
        }
        return $now->diffForHumans($endDateTime, ['parts' => 1, 'short' => true]);
    }

    public function getOnlineAttribute(): bool
    {
        $now = Carbon::now();
        $today = strtolower($now->format('l'));

        // If not a work day, return false
        if (!in_array($today, $this->work_days)) {
            return false;
        }

        $startTime = Carbon::createFromFormat('H:i', $this->start_time->format('H:i'));
        $endTime = Carbon::createFromFormat('H:i', $this->end_time->format('H:i'));

        $startDateTime = $now->copy()->setTimeFrom($startTime);
        $endDateTime = $now->copy()->setTimeFrom($endTime);

        // Return true only if current time is between start and end time
        if ($now->between($startDateTime, $endDateTime)) {
            return true;
        }

        // If current time is less than start time, return false (not started yet)
        if ($now->lessThan($startDateTime)) {
            return false;
        }

        // If we're past end time, return false
        return false;
    }
}
