<?php

namespace App\Console\Commands;

use App\Repositories\PostRepository;
use Illuminate\Console\Command;

class CheckExpiredPosts extends Command
{
    protected $signature = 'posts:check-expired';
    protected $description = 'Check and update expired posts';

    public function handle(PostRepository $postRepository)
    {
        $count = $postRepository->checkExpiredPosts();
        $this->info("Processed {$count} expired posts.");
    }
}
