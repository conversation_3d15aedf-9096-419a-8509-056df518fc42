<?php

namespace App\Mail;

use App\Models\Client;
use App\Models\SenderEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class WelcomeClientMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected Client $client,
        protected SenderEmail $senderEmail
    ) {
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            from: $this->senderEmail->email,
            subject: 'Welcome to ' . config('app.name'),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.clients.welcome',
            with: [
                'clientName' => $this->client->name,
                'userName' => $this->client->user->email,
                'loginUrl' => config('app.frontend_url'),
            ],
        );
    }
}

