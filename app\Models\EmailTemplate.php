<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailTemplate extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'subject',
        'content',
        'thumbnail_path',
        'sender_email_id'
    ];

    protected $appends = ['thumbnail_url'];

    public function senderEmail()
    {
        return $this->belongsTo(SenderEmail::class);
    }

    public function getThumbnailUrlAttribute()
    {
        return $this->thumbnail_path ? url('storage/' . $this->thumbnail_path) : null;
    }
}