<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('team_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained()->onDelete('cascade');
            $table->string('whatsapp_number')->nullable();
            $table->boolean('chat_disabled')->default(false);
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->json('work_days')->default(json_encode(['monday', 'tuesday', 'wednesday', 'thursday', 'friday']));
            $table->timestamps();
            $table->softDeletes();
        });

        // Create pivot table for team members and clients
        Schema::create('client_team_member', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_member_id')->constrained('team_members')->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
            $table->unique(['team_member_id', 'client_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('client_team_member');
        Schema::dropIfExists('team_members');
    }
};
