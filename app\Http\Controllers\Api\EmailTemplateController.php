<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\EmailTemplate\SendEmailRequest;
use App\Http\Requests\EmailTemplate\StoreEmailTemplateRequest;
use App\Http\Requests\EmailTemplate\UpdateEmailTemplateRequest;
use App\Http\Resources\EmailTemplateResource;
use App\Mail\CustomEmail;
use App\Models\Client;
use App\Models\SenderEmail;
use App\Repositories\EmailTemplateRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;

class EmailTemplateController extends Controller
{
    public function __construct(protected EmailTemplateRepository $emailTemplateRepository)
    {
    }

    public function index(Request $request)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);

        $perPage = $request->input('per_page', 10);
        $templates = $this->emailTemplateRepository->paginate($perPage);

        return EmailTemplateResource::collection($templates);
    }

    public function store(StoreEmailTemplateRequest $request)
    {
        $template = $this->emailTemplateRepository->create($request->validated());

        return (new EmailTemplateResource($template))
            ->additional(['message' => 'Email template created successfully'])
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    public function show($id)
    {
        $template = $this->emailTemplateRepository->find($id);
        return new EmailTemplateResource($template);
    }

    public function update(UpdateEmailTemplateRequest $request, $id)
    {
        $template = $this->emailTemplateRepository->update($id, $request->validated());

        return (new EmailTemplateResource($template))
            ->additional(['message' => 'Email template updated successfully']);
    }

    public function destroy($id)
    {
        $this->emailTemplateRepository->delete($id);

        return response()->json([
            'message' => 'Email template deleted successfully'
        ]);
    }

    public function sendEmail(SendEmailRequest $request)
    {
        $client = Client::findOrFail($request->client_id);
        $senderEmail = SenderEmail::findOrFail($request->sender_email_id);

        try {
            // Send the email
            Mail::to($client->user->email)
                ->send(new CustomEmail(
                    $request->subject,
                    $request->content,
                    $senderEmail
                ));

            // Save as template if requested
            $templateId = null;
            if ($request->save_as_template) {
                $templateData = [
                    'name' => $request->template_name,
                    'description' => $request->template_description,
                    'subject' => $request->subject,
                    'content' => $request->content,
                    'sender_email_id' => $request->sender_email_id
                ];

                if ($request->has('thumbnail')) {
                    $templateData['thumbnail'] = $request->thumbnail;
                }

                $template = $this->emailTemplateRepository->create($templateData);
                $templateId = $template->id;
            }

            return response()->json([
                'message' => 'Email sent successfully',
                'sent_to' => [
                    'client_name' => $client->name,
                    'email' => $client->email,
                ],
                'sent_from' => [
                    'email' => $senderEmail->email,
                    'is_default' => $senderEmail->is_default
                ],
                'template_saved' => $request->save_as_template,
                'template_id' => $templateId
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send email',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}