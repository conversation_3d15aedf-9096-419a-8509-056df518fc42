<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            
            // Foreign keys
            $table->foreignId('user_id')
                ->constrained()
                ->onDelete('cascade');
                
            $table->foreignId('admin_user_id')
                ->constrained('users')
                ->onDelete('cascade');
                
            $table->foreignId('post_id')
                ->constrained()
                ->onDelete('cascade');

            // Notification content
            $table->text('message');
            $table->string('action_type');
            $table->boolean('is_read')->default(false);
            
            // Timestamps and soft delete
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better query performance
            $table->index(['user_id', 'is_read']);
            $table->index('created_at');
            $table->index('action_type');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};