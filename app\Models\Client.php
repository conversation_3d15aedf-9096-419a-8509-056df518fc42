<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Client extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'phone',
        'address',
        'is_active',
        'user_id',
    ];
    protected $casts = [
        'is_active' => 'boolean',
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function embedCodes(): HasMany
    {
        return $this->hasMany(EmbedCode::class);
    }
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }
    public function teamMembers()
    {
        return $this->belongsToMany(TeamMember::class, 'client_team_member')->withTimestamps();
    }
    // Add accessor methods to get name and email from user
    public function getNameAttribute()
    {
        return $this->user->name;
    }

    public function getEmailAttribute()
    {
        return $this->user->email;
    }

}
