<?php

namespace App\Http\Controllers\Api\Auth;

use App\Enums\Platform;
use App\Http\Controllers\Controller;
use App\Http\Resources\EmbedCodeResource;
use App\Http\Requests\EmbedCode\StoreEmbedCodeRequest;
use App\Http\Requests\EmbedCode\UpdateEmbedCodeRequest;
use App\Repositories\EmbedCodeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EmbedCodeController extends Controller
{
    public function __construct(protected EmbedCodeRepository $embedCodeRepository)
    {
    }

    public function index(Request $request)
    {
        $request->validate([
            'client_id' => 'nullable|exists:clients,id',
            'client_ids' => 'nullable|string', // Comma-separated list of client IDs
            'platforms' => 'nullable|string', // Comma-separated list of platforms
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $embedCodes = $this->embedCodeRepository->paginate(
            perPage: $request->input('per_page', 10),
            filters: $request->only(['client_id', 'client_ids', 'platforms'])
        );

        return EmbedCodeResource::collection($embedCodes);
    }

    public function store(StoreEmbedCodeRequest $request)
    {
        $embedCode = $this->embedCodeRepository->createWithClientId(
            $request->validated(),
            $request->client_id
        );

        return new EmbedCodeResource($embedCode);
    }

    public function show($id)
    {
        $embedCode = $this->embedCodeRepository->find($id);
        return new EmbedCodeResource($embedCode);
    }

    public function update(UpdateEmbedCodeRequest $request, $id)
    {
        $embedCode = $this->embedCodeRepository->updateWithClientId(
            $id,
            $request->validated(),
            $request->client_id
        );

        return new EmbedCodeResource($embedCode);
    }

    public function destroy($id)
    {
        $this->embedCodeRepository->delete($id);
        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    public function getByClient($clientId)
    {
        $embedCodes = $this->embedCodeRepository->getByClientId($clientId);
        return EmbedCodeResource::collection($embedCodes);
    }
    public function platforms(): JsonResponse
    {
        return response()->json([
            'data' => Platform::options()
        ]);
    }

    public function getByUserId($userId)
    {
        $embedCodes = $this->embedCodeRepository->getByUserId($userId);
        return EmbedCodeResource::collection($embedCodes);
    }

    public function getMyEmbedCodes(Request $request)
    {
        $userId = $request->user()->id;
        $embedCodes = $this->embedCodeRepository->getByUserId($userId);
        return EmbedCodeResource::collection($embedCodes);
    }
}

