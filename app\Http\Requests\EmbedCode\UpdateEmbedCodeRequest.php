<?php

namespace App\Http\Requests\EmbedCode;

use App\Enums\Platform;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateEmbedCodeRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'platform' => ['sometimes', new Enum(Platform::class)],
            'embed_code' => ['sometimes', 'string', 'regex:/^<iframe[^>]*src="[^"]*"[^>]*>.*<\/iframe>$/'],
            'client_id' => 'sometimes|exists:clients,id',
            'is_active' => 'boolean'
        ];
    }

    public function messages(): array
    {
        return [
            'embed_code.regex' => 'The embed code must be a valid iframe HTML tag.'
        ];
    }
}

