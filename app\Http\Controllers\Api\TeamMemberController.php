<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\TeamMember\StoreTeamMemberRequest;
use App\Http\Requests\TeamMember\UpdateTeamMemberRequest;
use App\Http\Resources\TeamMemberCollection;
use App\Http\Resources\TeamMemberResource;
use App\Repositories\TeamMemberRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TeamMemberController extends Controller
{
    public function __construct(protected TeamMemberRepository $teamMemberRepository)
    {
    }

    /**
     * Display a paginated list of team members
     *
     * @param Request $request
     * @return TeamMemberCollection
     */
    public function index(Request $request)
    {
        $request->validate([
            'client_id' => 'nullable|exists:clients,id',
            'client_ids' => 'nullable|string', // Comma-separated list of client IDs
            'roles' => 'nullable|string', // Comma-separated list of roles
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $teamMembers = $this->teamMemberRepository->paginate(
            perPage: $request->input('per_page', 10),
            filters: $request->only(['client_id', 'client_ids', 'roles'])
        );

        return TeamMemberResource::collection($teamMembers);
    }

    /**
     * Store a newly created team member
     *
     * @param StoreTeamMemberRequest $request
     * @return TeamMemberResource
     */
    public function store(StoreTeamMemberRequest $request)
    {
        $teamMemberData = $request->safe()->except(['user']);
        $userData       = $request->safe()->only(['user'])['user'];

        $teamMember = $this->teamMemberRepository->createWithUser($teamMemberData, $userData);

        return (new TeamMemberResource($teamMember))
            ->additional([
                'message' => 'Team member created successfully',
            ])
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    /**
     * Display the specified team member
     *
     * @param int $id
     * @return TeamMemberResource
     */
    public function show($id)
    {
        $teamMember = $this->teamMemberRepository->find($id);
        return new TeamMemberResource($teamMember);
    }

    /**
     * Update the specified team member
     *
     * @param UpdateTeamMemberRequest $request
     * @param int $id
     * @return TeamMemberResource
     */
    public function update(UpdateTeamMemberRequest $request, $id)
    {
        $teamMemberData = $request->safe()->except(['user']);
        $userData       = $request->safe()->only(['user'])['user'] ?? null;

        $teamMember = $this->teamMemberRepository->updateWithUser($id, $teamMemberData, $userData);

        return (new TeamMemberResource($teamMember))
            ->additional([
                'message' => 'Team member updated successfully',
            ]);
    }

    /**
     * Remove the specified team member
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $this->teamMemberRepository->deleteWithUser($id);

        return response()->json([
            'message' => 'Team member deleted successfully',
        ], Response::HTTP_OK);
    }

    /**
     * Toggle chat status for the specified team member
     *
     * @param int $id
     * @return TeamMemberResource
     */
    public function toggleChatStatus($id)
    {
        $teamMember = $this->teamMemberRepository->toggleChatStatus($id);

        return (new TeamMemberResource($teamMember))
            ->additional([
                'message' => 'Chat status toggled successfully',
            ]);
    }

    /**
     * Get team members by user
     *
     * @param int $userId
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getByUser($userId)
    {
        $teamMembers = $this->teamMemberRepository->getByUserId($userId);
        return TeamMemberResource::collection($teamMembers);
    }

    /**
     * Get currently available team members
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getAvailable()
    {
        $teamMembers = $this->teamMemberRepository->getAvailableTeamMembers();
        return TeamMemberResource::collection($teamMembers);
    }
}



