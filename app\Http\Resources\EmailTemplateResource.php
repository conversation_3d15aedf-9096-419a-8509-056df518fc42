<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmailTemplateResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'subject' => $this->subject,
            'content' => $this->content,
            'thumbnail_url' => $this->thumbnail_url,
            'sender_email_id' => $this->sender_email_id,
            'sender_email' => $this->whenLoaded('senderEmail', function() {
                return [
                    'id' => $this->senderEmail->id,
                    'email' => $this->senderEmail->email,
                    'is_default' => $this->senderEmail->is_default
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}