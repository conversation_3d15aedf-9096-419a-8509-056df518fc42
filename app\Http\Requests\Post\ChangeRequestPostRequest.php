<?php

namespace App\Http\Requests\Post;

use App\Http\Requests\BaseRequest;
use App\Models\Post;

class ChangeRequestPostRequest extends BaseRequest
{
    public function authorize(): bool
    {
        $post = Post::findOrFail($this->route('post'));
        return request()->user()->hasRole('client') &&
               request()->user()->client->id === $post->client_id;
    }

    public function rules(): array
    {
        return [
            'comment' => 'required|string|max:1000'
        ];
    }
}

