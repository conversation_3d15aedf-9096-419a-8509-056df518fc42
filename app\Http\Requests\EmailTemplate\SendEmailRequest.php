<?php
namespace App\Http\Requests\EmailTemplate;

use App\Http\Requests\BaseRequest;

class SendEmailRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return true;
        // return $this->user()->hasRole(['super_admin', 'account_manager']);
    }

    public function rules(): array
    {
        return [
            'client_id'            => 'required|exists:clients,id',
            'subject'              => 'required|string|max:255',
            'content'              => 'required|string',
            'sender_email_id'      => 'required|exists:sender_emails,id',
            'save_as_template'     => 'sometimes|boolean',
            'template_name'        => 'required_if:save_as_template,true|string|max:255',
            'template_description' => 'nullable|string',
            'thumbnail'            => ['nullable', 'string', 'regex:/^data:image\/(jpeg|png|jpg|gif);base64,[A-Za-z0-9+\/=]+$/'],
        ];
    }
}
