<?php

namespace App\Http\Requests\Notification;

use App\Http\Requests\BaseRequest;
use App\Models\Notification;

class MarkNotificationAsReadRequest extends BaseRequest
{
    public function authorize(): bool
    {
        $notification = Notification::findOrFail($this->route('id'));
        
        if ($this->user()->hasRole('super_admin')) {
            return true;
        }

        if ($this->user()->hasRole('client')) {
            return $notification->user_id === $this->user()->id;
        }

        if ($this->user()->hasRole('account_manager')) {
            return $notification->post->logs()
                ->where('user_id', $this->user()->id)
                ->where('action', 'created')
                ->exists();
        }

        return false;
    }

    public function rules(): array
    {
        return [];
    }
}