<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class EmbedCodeResource extends BaseResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'platform' => $this->platform->value,
            'platform_label' => $this->platform_label,
            'embed_code' => $this->embed_code,
            'client_id' => $this->client_id,
            'is_active' => $this->is_active,
            'client' => $this->whenLoaded('client', function() {
                return [
                    'id' => $this->client->id,
                    'phone' => $this->client->phone,
                    'address' => $this->client->address,
                    'is_active' => $this->client->is_active,
                    'user' => [
                        'id' => $this->client->user->id,
                        'name' => $this->client->user->name,
                        'email' => $this->client->user->email,
                    ]
                ];
            }),
            'created_at' => $this->formatDateTime($this->created_at),
            'updated_at' => $this->formatDateTime($this->updated_at)
        ];
    }
}

