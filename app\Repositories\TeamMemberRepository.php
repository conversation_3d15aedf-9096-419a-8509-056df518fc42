<?php

namespace App\Repositories;

use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class TeamMemberRepository extends BaseRepository
{
    public function __construct(TeamMember $model)
    {
        parent::__construct($model);
    }

    public function paginate($perPage = 10, array $filters = [])
    {
        $query = $this->model->with(['user.roles', 'clients'])
            ->latest('updated_at');

        // Filter by single client ID
        if (isset($filters['client_id'])) {
            $query->whereHas('clients', function($q) use ($filters) {
                $q->where('clients.id', $filters['client_id']);
            });
        }

        // Filter by multiple client IDs
        if (isset($filters['client_ids'])) {
            $clientIds = explode(',', $filters['client_ids']);
            if (!empty($clientIds)) {
                $query->whereHas('clients', function($q) use ($clientIds) {
                    $q->whereIn('clients.id', $clientIds);
                });
            }
        }

        // Filter by user roles
        if (isset($filters['roles'])) {
            $roles = explode(',', $filters['roles']);
            if (!empty($roles)) {
                $query->whereHas('user.roles', function($q) use ($roles) {
                    $q->whereIn('name', $roles);
                });
            }
        }

        return $query->paginate($perPage);
    }

    public function createWithUser(array $teamMemberData, array $userData)
    {
        return DB::transaction(function () use ($teamMemberData, $userData) {
            // Handle profile image upload if provided
            if (isset($userData['profile_image'])) {
                $userData['profile_image_path'] = $this->uploadProfileImage($userData['profile_image']);
                unset($userData['profile_image']);
            }

            // Create user first
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => Hash::make($userData['password']),
                'profile_image_path' => $userData['profile_image_path'] ?? null
            ]);

            // Assign role
            $user->assignRole($userData['role']);

            // Create team member with work days
            $teamMember = $this->create([
                'user_id' => $user->id,
                'start_time' => $teamMemberData['start_time'],
                'end_time' => $teamMemberData['end_time'],
                'whatsapp_number' => $teamMemberData['whatsapp_number'] ?? null,
                'chat_disabled' => $teamMemberData['chat_disabled'] ?? false,
                'work_days' => $teamMemberData['work_days'] ?? ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            ]);

            // Assign clients if provided and role is not account_manager
            // && $userData['role'] !== 'account_manager'
            if (isset($teamMemberData['client_ids']) ) {
                $teamMember->clients()->sync($teamMemberData['client_ids']);
            }

            return $teamMember->load(['user.roles', 'clients.user']);
        });
    }

    public function updateWithUser($id, array $teamMemberData, ?array $userData = null)
    {
        return DB::transaction(function () use ($id, $teamMemberData, $userData) {
            $teamMember = $this->find($id);

            // Update team member data
            $updateData = array_filter([
                'start_time' => $teamMemberData['start_time'] ?? null,
                'end_time' => $teamMemberData['end_time'] ?? null,
                'whatsapp_number' => $teamMemberData['whatsapp_number'] ?? null,
                'chat_disabled' => $teamMemberData['chat_disabled'] ?? null,
                'work_days' => $teamMemberData['work_days'] ?? null,
            ], function ($value) {
                return !is_null($value);
            });

            if (!empty($updateData)) {
                $teamMember->update($updateData);
            }

            // Update client assignments if provided and not an account manager
            // && !$teamMember->user->hasRole('account_manager')
            if (isset($teamMemberData['client_ids'])) {
                $teamMember->clients()->sync($teamMemberData['client_ids']);
            }

            // Update user data if provided
            if ($userData && $teamMember->user) {
                // Handle profile image update
                if (isset($userData['profile_image'])) {
                    if ($teamMember->user->profile_image_path) {
                        Storage::disk('public')->delete($teamMember->user->profile_image_path);
                    }
                    $userData['profile_image_path'] = $this->uploadProfileImage($userData['profile_image']);
                    unset($userData['profile_image']);
                }

                $updateUserData = array_filter([
                    'name' => $userData['name'] ?? null,
                    'email' => $userData['email'] ?? null,
                    'profile_image_path' => $userData['profile_image_path'] ?? null,
                ], function ($value) {
                    return !is_null($value);
                });

                if (isset($userData['password'])) {
                    $updateUserData['password'] = Hash::make($userData['password']);
                }

                $teamMember->user->update($updateUserData);

                // Update role if provided
                if (isset($userData['role'])) {
                    $teamMember->user->syncRoles([$userData['role']]);
                }
            }

            return $teamMember->load(['user.roles', 'clients.user']);
        });
    }

    public function deleteWithUser($id)
    {
        return DB::transaction(function () use ($id) {
            $teamMember = $this->find($id);

            // Delete user's profile image if exists
            if ($teamMember->user->profile_image_path) {
                Storage::disk('public')->delete($teamMember->user->profile_image_path);
            }

            // Delete user (this will cascade delete team member due to foreign key constraint)
            $teamMember->user->delete();

            return true;
        });
    }
    public function toggleChatStatus($id)
    {
        $teamMember = $this->find($id);
        $teamMember->update([
            'chat_disabled' => !$teamMember->chat_disabled
        ]);
        return $teamMember->load(['user.roles', 'clients.user']);
    }

    protected function uploadProfileImage($base64Image)
    {
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));
        $filename = 'profiles/' . uniqid() . '.jpg';
        Storage::disk('public')->put($filename, $imageData);
        return $filename;
    }

    public function getByUserId($userId)
    {
        return $this->model->with(['user.roles', 'clients'])
            ->whereHas('clients.user', function ($query) use ($userId) {
                $query->where('users.id', $userId);
            })
            ->orWhereHas('user', function ($query) use ($userId) {
                $query->where('users.id', $userId);
            })
            ->latest()
            ->get();
    }

    public function getAvailableTeamMembers()
    {
        $now = now();
        $currentDay = strtolower($now->format('l')); // Get current day name in lowercase
        $currentTime = $now->format('H:i:s');

        return $this->model->with(['user.roles', 'clients'])
            ->whereJsonContains('work_days', $currentDay)
            ->whereTime('start_time', '<=', $currentTime)
            ->whereTime('end_time', '>=', $currentTime)
            ->where('chat_disabled', false)
            ->get();
    }
}






