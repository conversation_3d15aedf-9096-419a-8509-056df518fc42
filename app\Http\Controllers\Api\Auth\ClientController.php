<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Http\Resources\ClientResource;
use App\Http\Requests\Client\StoreClientRequest;
use App\Http\Requests\Client\UpdateClientRequest;
use App\Http\Requests\Client\SendWelcomeEmailRequest;
use App\Mail\WelcomeClientMail;
use App\Repositories\ClientRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;
use App\Models\SenderEmail;

class ClientController extends Controller
{
    public function __construct(protected ClientRepository $clientRepository)
    {
    }

    public function index(Request $request)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:1000',
            'search' => 'nullable|string|max:255',
            'is_active' => 'nullable|boolean',
        ]);

        $perPage = $request->input('per_page', 10);
        $filters = $request->only(['search', 'is_active']);

        $clients = $this->clientRepository->paginateWithFilters($perPage, $filters);
        return ClientResource::collection($clients);
    }

    public function store(StoreClientRequest $request)
    {
        // $client = $this->clientRepository->create($request->validated());
        // return new ClientResource($client);
        $clientData = $request->safe()->except(['user']);
        $userData = $request->safe()->only(['user'])['user'];

        $client = $this->clientRepository->createWithUser($clientData, $userData);
        return new ClientResource($client);
    }

    public function show($id)
    {
        $client = $this->clientRepository->find($id);
        $client->load('user');
        return new ClientResource($client);
    }

    public function update(UpdateClientRequest $request, $id)
    {
        // $client = $this->clientRepository->update($id, $request->validated());
        // return new ClientResource($client);
        $clientData = $request->safe()->except(['user']);
        $userData = $request->safe()->only(['user'])['user'] ?? null;
        $client = $this->clientRepository->updateWithUser($id, $clientData, $userData);
        return new ClientResource($client);
    }

    public function destroy($id)
    {
        // $this->clientRepository->delete($id);
        $this->clientRepository->deleteWithUser($id);
        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * Send welcome email to the client
     *
     * @param SendWelcomeEmailRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendWelcomeEmail(SendWelcomeEmailRequest $request, $id)
    {
        $client = $this->clientRepository->find($id);
        $senderEmail = SenderEmail::findOrFail($request->sender_email_id);

        try {
            Mail::to($client->user->email)
                ->send(new WelcomeClientMail($client, $senderEmail));

            return response()->json([
                'message' => 'Welcome email sent successfully',
                'sent_to' => [
                    'client_name' => $client->name,
                    'email' => $client->user->email,
                ],
                'sent_from' => [
                    'email' => $senderEmail->email,
                    'is_default' => $senderEmail->is_default
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send welcome email',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}






