<?php

namespace App\Http\Requests\EmbedCode;

use App\Enums\Platform;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class StoreEmbedCodeRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'platform' => ['required', new Enum(Platform::class)],
            'embed_code' => ['required', 'string', 'regex:/^<iframe[^>]*src="[^"]*"[^>]*>.*<\/iframe>$/'],
            'client_id' => 'required|exists:clients,id',
            'is_active' => 'boolean'
        ];
    }

    public function messages(): array
    {
        return [
            'embed_code.regex' => 'The embed code must be a valid iframe HTML tag.'
        ];
    }
}

