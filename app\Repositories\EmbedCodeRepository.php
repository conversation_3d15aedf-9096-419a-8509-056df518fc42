<?php
namespace App\Repositories;

use App\Models\EmbedCode;

class EmbedCodeRepository extends BaseRepository
{
    public function __construct(EmbedCode $model)
    {
        parent::__construct($model);
    }

    public function paginate($perPage = 10, array $filters = [])
    {
        $query = $this->model->with('client.user')
            ->latest('updated_at');

        if (isset($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        // Filter by multiple client IDs
        if (isset($filters['client_ids'])) {
            $clientIds = explode(',', $filters['client_ids']);
            if (!empty($clientIds)) {
                $query->whereIn('client_id', $clientIds);
            }
        }

        // Filter by multiple platforms
        if (isset($filters['platforms'])) {
            $platformArray = explode(',', $filters['platforms']);
            if (!empty($platformArray)) {
                $query->whereIn('platform', $platformArray);
            }
        }

        return $query->paginate($perPage);
    }

    public function createWithClientId(array $data, $clientId)
    {
        $data['client_id'] = $clientId;
        return $this->create($data);
    }

    public function updateWithClientId($id, array $data, $clientId)
    {
        $data['client_id'] = $clientId;
        return $this->update($id, $data);
    }

    public function getByClientId($clientId)
    {
        return $this->model->with('client.user')
            ->where('client_id', $clientId)
            ->latest('updated_at')
            ->get();
    }

    public function findByClientId($id, $clientId)
    {
        return $this->model->with('client.user')
            ->where('client_id', $clientId)
            ->where('id', $id)
            ->firstOrFail();
    }

    public function getByUserId($userId)
    {
        return $this->model->with('client.user')
            ->whereHas('client', function($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->latest('updated_at')
            ->get();
    }
}





