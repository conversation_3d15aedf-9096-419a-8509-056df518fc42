<?php

namespace App\Enums;

enum NotificationType: string
{
    case POST_CREATED = 'post_created';
    case POST_UPDATED = 'post_updated';
    case POST_APPROVED = 'post_approved';
    case POST_PUBLISHED = 'post_published';
    case POST_CHANGE_REQUESTED = 'post_change_requested';
    case POST_PREVIEW_SENT = 'post_preview_sent';

    public function label(): string
    {
        return match($this) {
            self::POST_CREATED => 'Post Created',
            self::POST_UPDATED => 'Post Updated',
            self::POST_APPROVED => 'Post Approved',
            self::POST_PUBLISHED => 'Post Published',
            self::POST_CHANGE_REQUESTED => 'Changes Requested',
            self::POST_PREVIEW_SENT => 'Preview Sent',
        };
    }

    public function getMessage(string $postTitle, string $adminName): string
    {
        return match($this) {
            self::POST_CREATED => "New post \"$postTitle\" has been created by $adminName.",
            self::POST_UPDATED => "Post \"$postTitle\" has been updated by $adminName.",
            self::POST_APPROVED => "Your post \"$postTitle\" has been approved by $adminName.",
            self::POST_PUBLISHED => "Your post \"$postTitle\" has been published by $adminName.",
            self::POST_CHANGE_REQUESTED => "$adminName has requested changes for post \"$postTitle\".",
            self::POST_PREVIEW_SENT => "Preview for post \"$postTitle\" has been sent by $adminName.",
        };
    }
}