<?php

namespace App\Enums;

enum PostStatus: string
{
    case PENDING = 'pending';
    case APPROVED = 'approved';
    case CHANGE_REQUEST = 'change_request';
    case PUBLISHED = 'published';

    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pending',
            self::APPROVED => 'Approved',
            self::CHANGE_REQUEST => 'Change Request',
            self::PUBLISHED => 'Published',
        };
    }
}