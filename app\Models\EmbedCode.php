<?php

namespace App\Models;

use App\Enums\Platform;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmbedCode extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'platform',
        'embed_code',
        'client_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'platform' => Platform::class,
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
    public function getPlatformLabelAttribute(): string
    {
        return $this->platform->label();
    }

    // public function getPlatformIconAttribute(): string
    // {
    //     return $this->platform->icon();
    // }
}
