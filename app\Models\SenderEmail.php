<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SenderEmail extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'email',
        'is_default'
    ];

    protected $casts = [
        'is_default' => 'boolean'
    ];

    protected static function boot()
    {
        parent::boot();

        // When setting an email as default, unset all other defaults
        static::saving(function ($model) {
            if ($model->is_default) {
                static::where('id', '!=', $model->id)->update(['is_default' => false]);
            }
        });

        // Ensure at least one default email exists
        static::saved(function ($model) {
            if (static::where('is_default', true)->count() === 0) {
                static::first()->update(['is_default' => true]);
            }
        });
    }
}