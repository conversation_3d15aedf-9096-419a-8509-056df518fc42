<?php

namespace App\Http\Requests\TeamMember;

use App\Enums\Days;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreTeamMemberRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'whatsapp_number' => 'required|string',
            'chat_disabled' => 'sometimes|boolean',
            'work_days' => 'required|array|min:1',
            'work_days.*' => [new Enum(Days::class)],
            'client_ids' => 'sometimes|array',
            'client_ids.*' => 'exists:clients,id',
            'user.name' => 'required|string|max:255',
            'user.email' => 'required|email|unique:users,email',
            'user.password' => 'required|string|min:8',
            'user.role' => 'required|string|exists:roles,name',
            'user.profile_image' => 'sometimes|string'
        ];
    }
}

