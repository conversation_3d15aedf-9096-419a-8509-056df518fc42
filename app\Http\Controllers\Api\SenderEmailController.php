<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SenderEmail\StoreSenderEmailRequest;
use App\Http\Requests\SenderEmail\UpdateSenderEmailRequest;
use App\Http\Resources\SenderEmailResource;
use App\Repositories\SenderEmailRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SenderEmailController extends Controller
{
    public function __construct(protected SenderEmailRepository $senderEmailRepository)
    {
    }

    public function index(Request $request)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);

        $perPage = $request->input('per_page', 10);
        $senderEmails = $this->senderEmailRepository->paginate($perPage);

        return SenderEmailResource::collection($senderEmails);
    }

    public function store(StoreSenderEmailRequest $request)
    {
        $senderEmail = $this->senderEmailRepository->create($request->validated());

        return (new SenderEmailResource($senderEmail))
            ->additional(['message' => 'Sender email created successfully'])
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    public function show($id)
    {
        $senderEmail = $this->senderEmailRepository->find($id);
        return new SenderEmailResource($senderEmail);
    }

    public function update(UpdateSenderEmailRequest $request, $id)
    {
        $senderEmail = $this->senderEmailRepository->update($id, $request->validated());

        return (new SenderEmailResource($senderEmail))
            ->additional(['message' => 'Sender email updated successfully']);
    }

    public function destroy($id)
    {
        try {
            $this->senderEmailRepository->delete($id);
            return response()->json([
                'message' => 'Sender email deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);
        }
    }
}