<?php
namespace App\Models;

use App\Enums\PostStatus;
use App\Enums\PostType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'scheduled_at',
        'status',
        'type',
        'client_id',
        'change_request_comment',
        'published_at',
    ];

    protected $casts = [
        'status'       => PostStatus::class,
        'type'         => PostType::class,
        'scheduled_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function platforms(): HasMany
    {
        return $this->hasMany(PostPlatform::class);
    }

    public function mediaPhotos()
    {
        return $this->hasMany(MediaPhoto::class);
    }
    public function mediaVideos()
    {
        return $this->hasMany(MediaVideo::class);
    }
    public function logs(): HasMany
    {
        return $this->hasMany(PostLog::class);
    }

}
