<?php

namespace App\Policies;

use App\Models\Notification;
use App\Models\User;

class NotificationPolicy
{
    public function update(User $user, Notification $notification): bool
    {
        if ($user->hasRole('super_admin')) {
            return true;
        }

        if ($user->hasRole('client')) {
            return $notification->user_id === $user->id;
        }

        if ($user->hasRole('account_manager')) {
            return $notification->post->logs()
                ->where('user_id', $user->id)
                ->where('action', 'created')
                ->exists();
        }

        return false;
    }
}