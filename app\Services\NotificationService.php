<?php

namespace App\Services;

use App\Enums\NotificationType;
use App\Models\Notification;
use App\Models\Post;
use App\Models\User;

class NotificationService
{
    public function createPostNotification(
        Post $post,
        NotificationType $type,
        User $adminUser
    ): Notification {
        return Notification::create([
            'user_id' => $post->client->user_id,
            'admin_user_id' => $adminUser->id,
            'post_id' => $post->id,
            'message' => $type->getMessage($post->title, $adminUser->name),
            'action_type' => $type->value,
            'is_read' => false
        ]);
    }
}