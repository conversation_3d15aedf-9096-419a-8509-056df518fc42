<?php

namespace App\Repositories;

use App\Models\SenderEmail;

class SenderEmailRepository
{
    public function paginate($perPage = 10)
    {
        return SenderEmail::orderBy('is_default', 'desc')
            ->orderBy('email')
            ->paginate($perPage);
    }

    public function create(array $data)
    {
        return SenderEmail::create($data);
    }

    public function find($id)
    {
        return SenderEmail::findOrFail($id);
    }

    public function update($id, array $data)
    {
        $senderEmail = $this->find($id);
        $senderEmail->update($data);
        return $senderEmail;
    }

    public function delete($id)
    {
        $senderEmail = $this->find($id);
        
        // Prevent deletion if it's the last record
        if (SenderEmail::count() <= 1) {
            throw new \Exception('Cannot delete the last sender email account');
        }
        
        // If deleting default email, make another one default
        if ($senderEmail->is_default) {
            $newDefault = SenderEmail::where('id', '!=', $id)->first();
            $newDefault->update(['is_default' => true]);
        }
        
        return $senderEmail->delete();
    }

    public function getDefault()
    {
        return SenderEmail::where('is_default', true)->first();
    }
}