<?php
namespace Database\Seeders;

use App\Enums\Platform;
use App\Enums\PostStatus;
use App\Enums\PostType;
use App\Models\Client;
use App\Models\Post;
use App\Models\PostLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure the posts directory exists
        Storage::disk('public')->makeDirectory('posts');

        // Get super admin user for creation logs
        $superAdmin = User::whereHas('roles', function ($query) {
            $query->where('name', 'super_admin');
        })->first();

        $clients   = Client::all();
        $platforms = Platform::cases();
        $statuses  = PostStatus::cases();

        foreach ($clients as $client) {
            // Create 5-10 posts for each client
            $numberOfPosts = rand(5, 15);

            for ($i = 0; $i < $numberOfPosts; $i++) {
                $status = $statuses[array_rand($statuses)];
                // Randomly select 1-3 platforms
                $selectedPlatforms = collect($platforms)
                    ->random(rand(1, 3))
                    ->values()
                    ->all();

                // Set dates and determine type
                $now         = Carbon::now();
                $scheduledAt = $now->copy()->addDays(rand(1, 30));
                $type        = PostType::UPCOMING;
                $publishedAt = null;

                // If approved and scheduled date is in the past, make it published
                if ($status === PostStatus::APPROVED && rand(0, 1)) {
                    $scheduledAt = $now->copy()->subDays(rand(1, 15));
                    $publishedAt = $scheduledAt->copy()->addHours(rand(1, 24));
                    $type        = PostType::PUBLISHED;
                    $status      = PostStatus::PUBLISHED;
                }

                $postData = [
                    'content'                => "This is a sample post content. #sample #test #content",
                    'scheduled_at'           => $scheduledAt,
                    'status'                 => $status,
                    'type'                   =>  $status     == PostStatus::PUBLISHED?PostType::PUBLISHED: $type        = PostType::UPCOMING ,
                    'published_at'           => $publishedAt,
                    'change_request_comment' => $status === PostStatus::CHANGE_REQUEST ? 'Please revise the content and add more hashtags.' : null,
                ];

                $post = Post::firstOrCreate(
                    [
                        'title'     => "Sample Post #{$i} for {$client->user->name}",
                        'client_id' => $client->id,
                    ],
                    $postData
                );

                // Create platform entries
                foreach ($selectedPlatforms as $platform) {
                    $post->platforms()->firstOrCreate([
                        'platform' => $platform,
                    ]);
                }

                // Add multiple media photos (2-4 per post)
                $numPhotos = rand(2, 4);
                for ($j = 0; $j < $numPhotos; $j++) {
                    $path = $this->generatePostImage();
                    if ($path) {
                        $post->mediaPhotos()->create(['path' => $path]);
                    }
                }

                // Add multiple media videos (1-2 per post)
                $sampleVideos = [
                    "https://www.youtube.com/watch?v=oUmVFHlwZsI",
                    "https://youtu.be/_Zel-Eapb9Y?si=Excej4WHB_th4bKF",
                ];
                $numVideos = rand(1, 2);
                $videoUrls = collect($sampleVideos)->random($numVideos)->values();
                foreach ($videoUrls as $url) {
                    $post->mediaVideos()->create(['url' => $url]);
                }

                // Log creation by super admin
                PostLog::create([
                    'post_id'    => $post->id,
                    'user_id'    => $superAdmin->id,
                    'action'     => 'created',
                    'details'    => 'Post created by super admin',
                    'new_values' => array_merge(
                        ['title' => $post->title],
                        $postData,
                        ['platforms' => $selectedPlatforms]
                    ),
                ]);

                // Simulate a sequence of actions based on status
                if ($status === PostStatus::APPROVED) {
                    // First, simulate a change request by client
                    PostLog::create([
                        'post_id'    => $post->id,
                        'user_id'    => $client->user->id,
                        'action'     => 'change_requested',
                        'details'    => 'Please add more hashtags',
                        'old_values' => [
                            'status'                 => PostStatus::PENDING->value,
                            'change_request_comment' => null,
                        ],
                        'new_values' => [
                            'status'                 => PostStatus::CHANGE_REQUEST->value,
                            'change_request_comment' => 'Please add more hashtags',
                        ],
                    ]);

                    // Then, simulate an update by super admin
                    PostLog::create([
                        'post_id'    => $post->id,
                        'user_id'    => $superAdmin->id,
                        'action'     => 'updated',
                        'details'    => 'Updated post content with more hashtags',
                        'old_values' => [
                            'content' => 'This is a sample post content. #sample #test',
                            'status'  => PostStatus::CHANGE_REQUEST->value,
                        ],
                        'new_values' => [
                            'content' => 'This is a sample post content. #sample #test #content #updated',
                            'status'  => PostStatus::PENDING->value,
                        ],
                    ]);

                    // Finally, log approval by client
                    PostLog::create([
                        'post_id'    => $post->id,
                        'user_id'    => $client->user->id,
                        'action'     => 'approved',
                        'details'    => 'Post approved by client',
                        'old_values' => ['status' => PostStatus::PENDING->value],
                        'new_values' => ['status' => PostStatus::APPROVED->value],
                    ]);
                } elseif ($status === PostStatus::CHANGE_REQUEST) {
                    // Log change request by client
                    PostLog::create([
                        'post_id'    => $post->id,
                        'user_id'    => $client->user->id,
                        'action'     => 'change_requested',
                        'details'    => 'Please revise the content and add more hashtags.',
                        'old_values' => [
                            'status'                 => PostStatus::PENDING->value,
                            'change_request_comment' => null,
                        ],
                        'new_values' => [
                            'status'                 => PostStatus::CHANGE_REQUEST->value,
                            'change_request_comment' => 'Please revise the content and add more hashtags.',
                        ],
                    ]);
                }
            }
        }
    }

    private function generatePostImage(): ?string
    {
        try {
            $width    = rand(800, 1200);
            $height   = rand(600, 800);
            $imageUrl = "https://picsum.photos/{$width}/{$height}";

            $response = Http::get($imageUrl);
            if ($response->successful()) {
                $filename = 'posts/' . uniqid() . '.jpg';
                Storage::disk('public')->put($filename, $response->body());
                return $filename;
            }
        } catch (\Exception $e) {
            report($e);
        }

        return null;
    }
}
