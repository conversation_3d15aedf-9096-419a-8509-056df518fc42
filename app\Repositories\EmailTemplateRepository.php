<?php

namespace App\Repositories;

use App\Models\EmailTemplate;
use Illuminate\Support\Facades\Storage;

class EmailTemplateRepository extends BaseRepository
{
    public function __construct(EmailTemplate $model)
    {
        parent::__construct($model);
    }

    public function paginate($perPage = 10)
    {
        return $this->model->with('senderEmail')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function create(array $data)
    {
        if (isset($data['thumbnail']) && $data['thumbnail']) {
            $data['thumbnail_path'] = $this->uploadThumbnail($data['thumbnail']);
            unset($data['thumbnail']);
        }

        return parent::create($data);
    }

    public function update($id, array $data)
    {
        $template = $this->find($id);

        if (isset($data['thumbnail']) && $data['thumbnail']) {
            // Delete old thumbnail if exists
            if ($template->thumbnail_path) {
                Storage::disk('public')->delete($template->thumbnail_path);
            }
            
            $data['thumbnail_path'] = $this->uploadThumbnail($data['thumbnail']);
            unset($data['thumbnail']);
        }

        return parent::update($id, $data);
    }

    protected function uploadThumbnail($base64Image)
    {
        // Extract the image data from the base64 string
        $image = substr($base64Image, strpos($base64Image, ',') + 1);
        $image = base64_decode($image);
        
        // Generate a unique filename
        $filename = 'email-templates/' . uniqid() . '.png';
        
        // Store the image
        Storage::disk('public')->put($filename, $image);
        
        return $filename;
    }
}