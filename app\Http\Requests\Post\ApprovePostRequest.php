<?php
namespace App\Http\Requests\Post;

use App\Http\Requests\BaseRequest;
use App\Models\Post;

class ApprovePostRequest extends BaseRequest
{
    public function authorize(): bool
    {
        $post = Post::findOrFail($this->route('post'));
        return request()->user()->hasRole('client') &&
        request()->user()->client->id === $post->client_id;
    }

    public function rules(): array
    {
        return []; // No additional data needed for approval
    }
}
