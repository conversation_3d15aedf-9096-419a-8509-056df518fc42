<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class ClientResource extends BaseResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name, // This comes from the accessor
            'email' => $this->email, // This comes from the accessor
            'phone' => $this->phone,
            'address' => $this->address,
            'is_active' => $this->is_active,
            'user' => $this->whenLoaded('user', function() {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                    'profile_image_url' => $this->user->profile_image_url,
                ];
            }),
            'created_at' => $this->formatDateTime($this->created_at),
            'updated_at' => $this->formatDateTime($this->updated_at),
        ];
    }
}





