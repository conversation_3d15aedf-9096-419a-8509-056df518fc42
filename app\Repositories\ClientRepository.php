<?php

namespace App\Repositories;

use App\Models\Client;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class ClientRepository extends BaseRepository
{

    public function __construct(Client $model)
    {
        parent::__construct($model);
    }

    public function paginate($perPage = 10)
    {
        return $this->model->with('user')
            ->latest('updated_at')
            ->paginate($perPage);
    }

    public function paginateWithFilters($perPage = 10, array $filters = [])
    {
        $query = $this->model->with('user')
            ->latest('updated_at');

        // Filter by account manager if user is an account manager
        $user = request()->user();
        if ($user && $user->hasRole('account_manager')) {
            $teamMember = $user->teamMember;
            if ($teamMember) {
                // Use assigned_clients array if it exists
                if (!empty($teamMember->assigned_clients)) {
                    $query->whereIn('id', $teamMember->assigned_clients);
                } else {
                    // Otherwise use the clients relationship
                    $assignedClientIds = $teamMember->clients()->pluck('clients.id')->toArray();
                    if (!empty($assignedClientIds)) {
                        $query->whereIn('id', $assignedClientIds);
                    }
                }
            }
        }

        // Apply search filter
        if (isset($filters['search']) && !empty($filters['search'])) {
            $search = $filters['search'];
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            })
                ->orWhere('phone', 'like', "%{$search}%")
                ->orWhere('address', 'like', "%{$search}%");
        }

        // Apply active status filter
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        return $query->paginate($perPage);
    }

    public function createWithUser(array $clientData, array $userData)
    {
        return DB::transaction(function () use ($clientData, $userData) {
            // Handle profile image upload if provided
            if (isset($userData['profile_image'])) {
                $userData['profile_image_path'] = $this->uploadProfileImage($userData['profile_image']);
                unset($userData['profile_image']);
            }

            // Create user first
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => Hash::make($userData['password']),
                'profile_image_path' => $userData['profile_image_path'] ?? null,
            ]);

            $user->assignRole('client');

            // Create client with only relevant data
            $client = $this->create([
                'phone' => $clientData['phone'] ?? null,
                'address' => $clientData['address'] ?? null,
                'is_active' => $clientData['is_active'] ?? true,
                'user_id' => $user->id,
            ]);

            $client->load('user');
            return $client;
        });
    }

    public function updateWithUser($id, array $clientData, ?array $userData = null)
    {
        return DB::transaction(function () use ($id, $clientData, $userData) {
            $client = $this->find($id);
            // Update client data
            $clientUpdateData = [];

            // Explicitly check if keys exist in the array instead of using array_filter
            if (array_key_exists('phone', $clientData)) {
                $clientUpdateData['phone'] = $clientData['phone'];
            }

            if (array_key_exists('address', $clientData)) {
                $clientUpdateData['address'] = $clientData['address'];
            }

            if (array_key_exists('is_active', $clientData)) {
                $clientUpdateData['is_active'] = $clientData['is_active'];
            }

            if (!empty($clientUpdateData)) {
                $client->update($clientUpdateData);
            }

            // Update user data if provided
            if ($userData && $client->user) {
                // Handle profile image update
                if (isset($userData['profile_image'])) {
                    // Delete old image if exists
                    if ($client->user->profile_image_path) {
                        Storage::disk('public')->delete($client->user->profile_image_path);
                    }
                    $userData['profile_image_path'] = $this->uploadProfileImage($userData['profile_image']);
                    unset($userData['profile_image']);
                }

                $updateData = [];

                // Explicitly check if keys exist in the array
                if (array_key_exists('name', $userData)) {
                    $updateData['name'] = $userData['name'];
                }

                if (array_key_exists('email', $userData)) {
                    $updateData['email'] = $userData['email'];
                }

                if (array_key_exists('profile_image_path', $userData)) {
                    $updateData['profile_image_path'] = $userData['profile_image_path'];
                }

                if (isset($userData['password'])) {
                    $updateData['password'] = Hash::make($userData['password']);
                }

                $client->user->update($updateData);
            }

            $client->load('user');
            return $client;
        });
    }

    public function deleteWithUser($id)
    {
        return DB::transaction(function () use ($id) {
            $client = $this->find($id);
            // Soft delete all posts and their related data using PostRepository
            $postRepository = app(\App\Repositories\PostRepository::class);
            foreach ($client->posts as $post) {
                $postRepository->delete($post->id);
            }
            // Soft delete all embed codes
            $client->embedCodes()->delete();
            // Soft delete the client user
            if ($client->user) {
                $client->user->delete();
            }
            // Soft delete all client_team_member pivot records
            \DB::table('client_team_member')
                ->where('client_id', $client->id)
                ->update(['deleted_at' => now()]);
            // Soft delete the client
            return $client->delete();
        });
    }

    public function getAssignedClients()
    {
        $query = $this->model->with('user');

        // Get clients assigned to the logged-in account manager
        $user = request()->user();
        if ($user && $user->hasRole('account_manager')) {
            $teamMember = $user->teamMember;
            if ($teamMember) {
                // Use assigned_clients array if it exists
                if (!empty($teamMember->assigned_clients)) {
                    $query->whereIn('id', $teamMember->assigned_clients);
                } else {
                    // Otherwise use the clients relationship
                    $assignedClientIds = $teamMember->clients()->pluck('clients.id')->toArray();
                    if (!empty($assignedClientIds)) {
                        $query->whereIn('id', $assignedClientIds);
                    }
                }
            }
        }

        return $query->get();
    }

    protected function uploadProfileImage($base64Image)
    {
        // Extract the image data from base64 string
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));

        // Generate unique filename
        $filename = 'profiles/' . uniqid() . '.jpg';

        // Store the image
        Storage::disk('public')->put($filename, $imageData);

        return $filename;
    }
}
