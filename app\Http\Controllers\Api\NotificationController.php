<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\NotificationResource;
use App\Models\Notification;
use App\Http\Requests\Notification\MarkNotificationAsReadRequest;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
            'is_read' => 'nullable|in:true,false,1,0',
            'action_type' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $user = $request->user();
        $perPage = $request->input('per_page', 20);

        $query = Notification::with(['adminUser', 'post'])
            ->latest();

        // Apply role-based filters
        if ($user->hasRole('client')) {
            $query->where('user_id', $user->id);
        } elseif ($user->hasRole('account_manager')) {
            $query->whereHas('post', function ($q) use ($user) {
                $q->whereHas('logs', function ($q) use ($user) {
                    $q->where('user_id', $user->id)
                        ->where('action', 'created');
                });
            });
        }
        // super_admin sees all notifications

        // Apply read/unread filter
        if ($request->has('is_read')) {
            $isRead = filter_var($request->input('is_read'), FILTER_VALIDATE_BOOLEAN);
            $query->where('is_read', $isRead);
        }

        // Apply action type filter
        if ($request->filled('action_type')) {
            $query->where('action_type', $request->action_type);
        }

        // Apply date range filters
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $notifications = $query->paginate($perPage);

        return NotificationResource::collection($notifications)
            ->additional([
                'meta' => [
                    'unread_count' => $this->getUnreadCount($user),
                ],
            ]);
    }

    private function getUnreadCount($user)
    {
        $query = Notification::where('is_read', false);

        if ($user->hasRole('client')) {
            $query->where('user_id', $user->id);
        } elseif ($user->hasRole('account_manager')) {
            $query->whereHas('post', function ($q) use ($user) {
                $q->whereHas('logs', function ($q) use ($user) {
                    $q->where('user_id', $user->id)
                        ->where('action', 'created');
                });
            });
        }

        return $query->count();
    }

    public function markAsRead(MarkNotificationAsReadRequest $request, $id)
    {
        $notification = Notification::findOrFail($id);
        $notification->update(['is_read' => true]);

        return response()->json(['message' => 'Notification marked as read']);
    }

    public function markAllAsRead(Request $request)
    {
        $user = $request->user();
        $query = Notification::where('is_read', false);

        if ($user->hasRole('client')) {
            $query->where('user_id', $user->id);
        } elseif ($user->hasRole('account_manager')) {
            $query->whereHas('post', function ($q) use ($user) {
                $q->whereHas('logs', function ($q) use ($user) {
                    $q->where('user_id', $user->id)
                        ->where('action', 'created');
                });
            });
        }

        $query->update(['is_read' => true]);

        return response()->json(['message' => 'All notifications marked as read']);
    }
}


