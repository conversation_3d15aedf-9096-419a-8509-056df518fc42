<?php

namespace App\Http\Requests\Client;

use App\Http\Requests\BaseRequest;

class SendWelcomeEmailRequest extends BaseRequest
{
    public function authorize(): bool
    {
        // return $this->user()->hasRole(['super_admin', 'account_manager']);
        return true;
    }

    public function rules(): array
    {
        return [
            'sender_email_id' => 'required|exists:sender_emails,id'
        ];
    }
}
