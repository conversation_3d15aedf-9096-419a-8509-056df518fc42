<?php

namespace App\Http\Requests\EmailTemplate;

use App\Http\Requests\BaseRequest;

class UpdateEmailTemplateRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasRole(['super_admin', 'account_manager']);
    }

    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'subject' => 'sometimes|string|max:255',
            'content' => 'sometimes|string',
            'thumbnail' => ['nullable', 'string', 'regex:/^data:image\/(jpeg|png|jpg|gif);base64,[A-Za-z0-9+\/=]+$/'],
            'sender_email_id' => 'sometimes|exists:sender_emails,id'
        ];
    }
}