<?php
namespace App\Mail;

use App\Models\Post;
use App\Models\SenderEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PostPreviewNotification extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected Post $post,
        protected SenderEmail $senderEmail
    ) {
    }

    public function build()
    {
        $platformsList = $this->post->platforms->map(function ($platform) {
            return $platform->platform->label();
        })->join(', ');

        return $this->from($this->senderEmail->email)
            ->subject('New Post Ready for Review - ' . $this->post->title)
            ->view('emails.posts.preview')
            ->with([
                'post'          => $this->post,
                'clientName'    => $this->post->client->name,
                'platforms'     => $platformsList,
                'previewUrl'    => config('app.frontend_url') . '/client/calendar',
                'scheduledDate' => $this->post->scheduled_at->format('F j, Y \a\t g:i A'),
            ]);
    }
}

