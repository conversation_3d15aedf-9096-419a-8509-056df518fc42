<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\UserResource;
use App\Http\Resources\ClientResource;

class TeamMemberResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'start_time' => $this->start_time ? $this->start_time->format('H:i') : null,
            'end_time' => $this->end_time ? $this->end_time->format('H:i') : null,
            'whatsapp_number' => $this->whatsapp_number,
            'chat_disabled' => $this->chat_disabled,
            'work_days' => $this->work_days,
            'last_active_at' => $this->last_active_at,
            'online' => $this->online,
            'user' => new UserResource($this->whenLoaded('user')),
            'clients' => ClientResource::collection($this->whenLoaded('clients')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}




