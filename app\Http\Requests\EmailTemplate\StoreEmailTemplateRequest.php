<?php

namespace App\Http\Requests\EmailTemplate;

use App\Http\Requests\BaseRequest;

class StoreEmailTemplateRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasRole(['super_admin', 'account_manager']);
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'thumbnail' => ['nullable', 'string', 'regex:/^data:image\/(jpeg|png|jpg|gif);base64,[A-Za-z0-9+\/=]+$/'],
            'sender_email_id' => 'required|exists:sender_emails,id'
        ];
    }
}