<?php
namespace App\Http\Requests\TeamMember;

use App\Enums\Days;
use App\Models\TeamMember;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateTeamMemberRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // Get the team member ID from the route parameter 'team_member' instead of 'id'
        $teamMemberId = $this->route('team_member');

        // Get the user_id associated with this team member
        $userId = TeamMember::findOrFail($teamMemberId)->user_id;

        return [
            'start_time'         => 'sometimes|date_format:H:i',
            'end_time'           => 'sometimes|date_format:H:i|after:start_time',
            'whatsapp_number'    => 'sometimes|string',
            'chat_disabled'      => 'sometimes|boolean',
            'work_days'          => 'sometimes|array|min:1',
            'work_days.*'        => [new Enum(Days::class)],
            'client_ids'         => 'sometimes|array',
            'client_ids.*'       => 'exists:clients,id',
            'user'               => 'sometimes|array',
            'user.name'          => 'sometimes|string|max:255',
            'user.email'         => 'sometimes|email|unique:users,email,' . $userId,
            'user.password'      => 'sometimes|string|min:8',
            'user.role'          => 'sometimes|string|exists:roles,name',
            'user.profile_image' => ['nullable', 'string', 'regex:/^data:image\/(jpeg|png|jpg|gif);base64,[A-Za-z0-9+\/=]+$/'],

        ];
    }
}
