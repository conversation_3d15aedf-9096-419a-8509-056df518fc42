<?php
namespace Database\Seeders;

use App\Enums\Platform;
use App\Models\Client;
use App\Models\EmbedCode;
use Illuminate\Database\Seeder;

class EmbedCodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all clients
        $clients = Client::all();

        $lookerStudioEmbed = '<iframe width="100%" height="750px" src="https://lookerstudio.google.com/embed/reporting/1560954b-1e51-4f34-a677-6f7400fc49f3/page/g1hBF" frameborder="0" style="border:0" allowfullscreen sandbox="allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"></iframe>';

        // Sample embed codes for each platform (all using Looker Studio)
        $embedCodeTemplates = array_fill_keys(
            array_map(fn($platform) => $platform->value, Platform::cases()),
            $lookerStudioEmbed
        );

        foreach ($clients as $client) {
            // Create 2-3 embed codes for each client
            $numberOfCodes = rand(2, 3);

            for ($i = 0; $i < $numberOfCodes; $i++) {
                // Randomly select a platform
                $platform = Platform::cases()[array_rand(Platform::cases())];

                EmbedCode::firstOrCreate(
                    [
                        'platform' => $platform,
                        'client_id' => $client->id
                    ],
                    [
                        'embed_code' => $lookerStudioEmbed,
                        'is_active' => true,
                    ]
                );
            }
        }
    }
}

