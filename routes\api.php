<?php

use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\Auth\ClientController;
use App\Http\Controllers\Api\Auth\EmbedCodeController;
use App\Http\Controllers\Api\EmailTemplateController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\PostController;
use App\Http\Controllers\Api\SenderEmailController;
use App\Http\Controllers\Api\TeamMemberController;
use App\Http\Controllers\Api\UserActionLogController;
use App\Http\Controllers\Api\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Public Routes
|--------------------------------------------------------------------------
*/
Route::post('/login', [AuthController::class, 'login']);

/*
|--------------------------------------------------------------------------
| Protected Routes
|--------------------------------------------------------------------------
*/
Route::middleware('auth:sanctum')->group(function () {
    // User info
    Route::get('/user/info', [UserController::class, 'info']);

    // Auth
    Route::post('/logout', [AuthController::class, 'logout']);

    // Common endpoints
    Route::get('platforms', [EmbedCodeController::class, 'platforms']);
    Route::get('user/action-logs', [UserActionLogController::class, 'index']);
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('{id}/mark-as-read', [NotificationController::class, 'markAsRead']);
        Route::post('mark-all-as-read', [NotificationController::class, 'markAllAsRead']);
    });

    /*
    |--------------------------------------------------------------------------
    | Super Admin & Account Manager Shared Routes (Client Management, Team Member Management, Post Management)
    |--------------------------------------------------------------------------
    */
    Route::middleware('role:super_admin|account_manager')->group(function () {
        // Client Management
        Route::apiResource('clients', ClientController::class);
        Route::post('clients/{id}/welcome-email', [ClientController::class, 'sendWelcomeEmail']);

        // Team Member Management
        Route::apiResource('team-members', TeamMemberController::class);
        Route::prefix('team-members')->group(function () {
            Route::put('{id}/toggle-chat', [TeamMemberController::class, 'toggleChatStatus']);
            Route::get('available', [TeamMemberController::class, 'getAvailable']);
        });

        // Post Management
        Route::apiResource('posts', PostController::class)->except(['show']);
        Route::prefix('posts')->group(function () {
            Route::post('{post}/publish', [PostController::class, 'publish']);
            Route::get('{post}/logs', [PostController::class, 'logs']);
            Route::post('{post}/send-preview', [PostController::class, 'sendPreviewEmail']);
        });

        // User Management
        Route::get('users/{userId}/team-members', [TeamMemberController::class, 'getByUser']);

        // Sender Email Management
        Route::apiResource('sender-emails', SenderEmailController::class);
    });

    /*
    |--------------------------------------------------------------------------
    | Super Admin Only Routes (Embed Code Management)
    |--------------------------------------------------------------------------
    */
    Route::middleware('role:super_admin')->group(function () {
        // Embed Code Management (Super Admin Only)
        Route::apiResource('embed-codes', EmbedCodeController::class);
        Route::prefix('embed-codes')->group(function () {
            Route::get('users/{userId}', [EmbedCodeController::class, 'getByUserId']);
        });
    });

    /*
    |--------------------------------------------------------------------------
    | Client Routes
    |--------------------------------------------------------------------------
    */
    Route::middleware('role:client')->group(function () {
        // Embed Codes
        Route::get('my-embed-codes', [EmbedCodeController::class, 'getMyEmbedCodes']);

        // Team Members
        Route::get('users/{userId}/team-members', [TeamMemberController::class, 'getByUser']);

        // Posts
        Route::prefix('my-posts')->group(function () {
            Route::get('', [PostController::class, 'myPosts']);
            Route::post('{post}/approve', [PostController::class, 'approve']);
            Route::post('{post}/change-request', [PostController::class, 'changeRequest']);
            Route::get('{post}/logs', [PostController::class, 'logs']);
        });
    });

    // Expose GET /posts/{id} to all roles (super_admin, account_manager, client)
    Route::middleware('role:super_admin,account_manager,client')->get('posts/{post}', [PostController::class, 'show']);

    // Email Templates
    Route::middleware(['auth:sanctum', 'role:super_admin,account_manager'])->group(function () {
        Route::apiResource('email-templates', EmailTemplateController::class);
        Route::post('email-templates/send', [EmailTemplateController::class, 'sendEmail']);
    });
});
