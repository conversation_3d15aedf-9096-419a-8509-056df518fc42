<?php

namespace App\Http\Requests\SenderEmail;

use App\Http\Requests\BaseRequest;

class UpdateSenderEmailRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasRole('super_admin');
    }

    public function rules(): array
    {
        return [
            'email' => 'sometimes|email|unique:sender_emails,email,' . $this->route('sender_email'),
            'is_default' => 'sometimes|boolean'
        ];
    }
}