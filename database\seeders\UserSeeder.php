<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin
        $password = '@dm!n#2024$uP3R%';
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make($password),
                'profile_image_path' => $this->generateProfileImage()
            ]
        );
        $user->assignRole('super_admin');
        // $this->logPassword('<EMAIL>', $password, 'super_admin');
    }

    protected function generateProfileImage(): ?string
    {
        try {
            // Generate a square image for profile
            $size = 400; // 400x400 pixels
            $imageUrl = "https://picsum.photos/{$size}/{$size}";

            $response = Http::get($imageUrl);
            if ($response->successful()) {
                $filename = 'profiles/' . uniqid() . '.jpg';
                Storage::disk('public')->put($filename, $response->body());
                return $filename;
            }
        } catch (\Exception $e) {
            report($e);
        }

        return null;
    }

    // protected function logPassword(string $email, string $password, string $role): void
    // {
    //     $logPath = storage_path('logs/seeder_passwords.log');
    //     $content = "[" . now() . "] {$role}: {$email} - {$password}" . PHP_EOL;
    //     file_put_contents($logPath, $content, FILE_APPEND);
    // }
}




