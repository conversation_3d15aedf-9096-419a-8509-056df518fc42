<?php
namespace App\Http\Controllers\Api;

use App\Enums\NotificationType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Post\ApprovePostRequest;
use App\Http\Requests\Post\ChangeRequestPostRequest;
use App\Http\Requests\Post\PublishPostRequest;
use App\Http\Requests\Post\SendPreviewEmailRequest;
use App\Http\Requests\Post\StorePostRequest;
use App\Http\Requests\Post\UpdatePostRequest;
use App\Http\Resources\PostLogResource;
use App\Http\Resources\PostResource;
use App\Mail\PostPreviewNotification;
use App\Models\Post;
use App\Models\SenderEmail;
use App\Repositories\PostRepository;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;

class PostController extends Controller
{
    protected $notificationService;
    protected $postRepository;

    public function __construct(
        PostRepository $postRepository,
        NotificationService $notificationService
    ) {
        $this->postRepository      = $postRepository;
        $this->notificationService = $notificationService;
    }

    public function index(Request $request)
    {
        $request->validate([
            'page'         => 'nullable|integer|min:1',
            'per_page'     => 'nullable|integer|min:1|max:100',
            'client_ids'   => 'nullable|string', // Comma-separated list of client IDs
            'platforms'    => 'nullable|string', // Comma-separated list of platforms
            'title_search' => 'nullable|string|max:255',
            'status'       => 'nullable|string',
            'sort_field'   => 'nullable|string|in:title,scheduled_at,created_at,updated_at,status',
            'sort_order'   => 'nullable|string|in:asc,desc',
        ]);

        $filters = [
            'month'        => $request->input('month'),
            'year'         => $request->input('year'),
            'status'       => $request->input('status'),
            'platform'     => $request->input('platform'),
            'client_id'    => $request->input('client_id'),
            'client_ids'   => $request->input('client_ids'),
            'platforms'    => $request->input('platforms'),
            'title_search' => $request->input('title_search'),
            'sort_field'   => $request->input('sort_field', 'updated_at'),
            'sort_order'   => $request->input('sort_order', 'desc'),
        ];

        $perPage = $request->input('per_page', 10);
        $posts   = $this->postRepository->getFilteredPosts($filters, $perPage);

        return PostResource::collection($posts);
    }

    public function store(StorePostRequest $request)
    {
        $data = $request->validated();
        $post = $this->postRepository->create($data);
        // Create notification for new post
        $this->notificationService->createPostNotification(
            $post,
            NotificationType::POST_CREATED,
            $request->user()
        );

        return new PostResource($post);
    }

    public function show($id)
    {
        $post = $this->postRepository->getPostById($id);
        return new PostResource($post);
    }

    public function update(UpdatePostRequest $request, $id)
    {
        $data                            = $request->validated();
        $data['deleted_media_photo_ids'] = $request->input('deleted_media_photo_ids', []);
        $data['delete_media_photo_id']   = $request->input('delete_media_photo_id');
        $post                            = $this->postRepository->update($id, $data);
        return new PostResource($post);
    }

    public function destroy($id)
    {
        $this->postRepository->delete($id);
        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    public function approve(ApprovePostRequest $request, $id)
    {
        $this->postRepository->approve($id);
        $post = Post::query()->findOrFail($id);
        // Create notification for approval
        $this->notificationService->createPostNotification(
            $post,
            NotificationType::POST_APPROVED,
            $request->user()
        );

        return new PostResource($post);
    }

    public function changeRequest(ChangeRequestPostRequest $request, $id)
    {
        $this->postRepository->changeRequest($id, $request->comment);
        $post = Post::query()->findOrFail($id);
        // Create notification for change request
        $this->notificationService->createPostNotification(
            $post,
            NotificationType::POST_CHANGE_REQUESTED,
            $request->user()
        );

        return new PostResource($post);
    }

    public function publish(PublishPostRequest $request, $id)
    {
        $this->postRepository->publish($id);
        $post = Post::query()->findOrFail($id);
        // Create notification for publishing
        $this->notificationService->createPostNotification(
            $post,
            NotificationType::POST_PUBLISHED,
            $request->user()
        );

        return new PostResource($post);
    }

    public function myPosts(Request $request)
    {
        $filters = $request->only([
            'month',
            'year',
            'status',
            'platform',
        ]);

        $filters['client_id'] = $request->user()->client->id;
        $perPage              = $request->input('per_page', 10);
        $posts                = $this->postRepository->getFilteredPosts($filters, $perPage);

        return PostResource::collection($posts);
    }

    public function logs($id)
    {
        $post = $this->postRepository->find($id);
        return PostLogResource::collection($post->logs()->with('user')->latest()->get());
    }

    public function sendPreviewEmail(SendPreviewEmailRequest $request, $id)
    {
        $post        = $this->postRepository->find($id);
        $senderEmail = SenderEmail::findOrFail($request->sender_email_id);

        try {
            Mail::to($post->client->email)
                ->send(new PostPreviewNotification($post, $senderEmail));

            // Log the email sent action
            $this->postRepository->logAction(
                $post,
                'email_sent',
                'Preview email sent to client',
                null,
                ['additional_message' => $request->additional_message]
            );

            // Create notification for preview sent
            $this->notificationService->createPostNotification(
                $post,
                NotificationType::POST_PREVIEW_SENT,
                $request->user()
            );

            return response()->json([
                'message'   => 'Preview email sent successfully',
                'sent_to'   => [
                    'client_email' => $post->client->email,
                    'user_email'   => $post->client->user->email,
                ],
                'sent_from' => [
                    'email'      => $senderEmail->email,
                    'is_default' => $senderEmail->is_default,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send preview email',
                'error'   => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
