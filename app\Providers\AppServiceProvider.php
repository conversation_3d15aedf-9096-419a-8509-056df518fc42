<?php

namespace App\Providers;

use App\Models\SenderEmail;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set default mail from address from database
        try {
            $defaultSenderEmail = SenderEmail::where('is_default', true)->first();
            if ($defaultSenderEmail) {
                Config::set('mail.from.address', $defaultSenderEmail->email);
            }
        } catch (\Exception $e) {
            // Handle database connection errors or table not exists
        }
    }
}

