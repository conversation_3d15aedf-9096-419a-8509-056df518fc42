<?php

namespace App\Mail;

use App\Models\SenderEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class CustomEmail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected string $emailSubject,
        protected string $emailContent,
        protected SenderEmail $senderEmail
    ) {
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            from: $this->senderEmail->email,
            subject: $this->emailSubject,
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.custom',
            with: [
                'content' => $this->emailContent,
            ],
        );
    }
}