<?php
namespace App\Repositories;

use App\Enums\PostStatus;
use App\Enums\PostType;
use App\Models\Post;
use App\Models\PostLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class PostRepository extends BaseRepository
{
    public function __construct(Post $model)
    {
        parent::__construct($model);
    }

    public function logAction(Post $post, string $action, ?string $details = null, ?array $oldValues = null, ?array $newValues = null, ?int $userId = null)
    {
        PostLog::create([
            'post_id'    => $post->id,
            'user_id'    => $userId ?? (request()->user()?->id ?? null),
            'action'     => $action,
            'details'    => $details,
            'old_values' => $oldValues,
            'new_values' => $newValues,
        ]);
    }

    public function create(array $data)
    {
        // Extract media_video_url
        $mediaVideoUrls = $data['media_video_url'] ?? [];
        unset($data['media_video_url']);

        // Handle mediaPhotos upload (multi images)
        $mediaPhotos = $data['media_photos'] ?? [];
        unset($data['media_photos']);

        // Remove image upload for backward compatibility
        $platforms = $data['platforms'] ?? [];
        unset($data['platforms']);

        // Create the post
        $post = parent::create($data);

        // Create platform entries
        if (! empty($platforms)) {
            foreach ($platforms as $platform) {
                $post->platforms()->create(['platform' => $platform]);
            }
        }

        // Create media photos entries
        foreach ($mediaPhotos as $image) {
            $path = $this->uploadImage($image);
            $post->mediaPhotos()->create(['path' => $path]);
        }

        // Create media videos entries
        foreach ($mediaVideoUrls as $url) {
            $post->mediaVideos()->create(['url' => $url]);
        }

        $this->logAction($post, 'created', 'Post created', null, $data);
        return $post->load(['platforms', 'mediaPhotos', 'mediaVideos']);
    }

    public function update($id, array $data)
    {
        $post      = $this->find($id);
        $oldValues = $post->toArray();
        // Extract media_video_url
        $mediaVideosUrls = $data['media_videos_url'] ?? [];
        unset($data['media_videos_url']);

        // Handle deletion of specific media photos
        $deletedMediaPhotoIds = $data['deleted_media_photo_ids'] ?? [];
        unset($data['deleted_media_photo_ids']);

        foreach ($deletedMediaPhotoIds as $photoId) {
            $mediaPhoto = $post->mediaPhotos()->find($photoId);
            if ($mediaPhoto) {
                \Storage::disk('public')->delete($mediaPhoto->path);
                $mediaPhoto->delete();
            }
        }

        // Handle mediaPhotos upload (multi images)
        $mediaPhotos = $data['media_photos'] ?? [];
        unset($data['media_photos']);

        foreach ($mediaPhotos as $image) {
            $path = $this->uploadImage($image);
            $post->mediaPhotos()->create(['path' => $path]);
        }
        // Remove all existing media videos and add new ones
        $post->mediaVideos()->delete();
        foreach ($mediaVideosUrls as $url) {
            $post->mediaVideos()->create(['url' => $url]);
        }

        // Handle platforms update if provided
        if (isset($data['platforms'])) {
            // Delete existing platforms
            $post->platforms()->delete();

            // Create new platform entries
            foreach ($data['platforms'] as $platform) {
                $post->platforms()->create(['platform' => $platform]);
            }

            unset($data['platforms']);
        }

        // Set status to pending after update
        $data['status'] = PostStatus::PENDING;

        // Update other post data
        $post = parent::update($id, $data);

        $this->logAction(
            $post,
            'updated',
            'Post updated',
            array_intersect_key($oldValues, $data),
            $data
        );

        return $post->load(['platforms', 'mediaPhotos', 'mediaVideos']);
    }

    public function delete($id)
    {
        $post = $this->find($id);

        // Soft delete related data
        $post->platforms()->delete();
        $post->mediaPhotos()->delete();
        $post->mediaVideos()->delete();
        $post->logs()->delete();

        // Soft delete the post
        $this->logAction($post, 'deleted', 'Post deleted', $post->toArray());
        return $post->delete();
    }

    public function approve($id)
    {
        $post      = $this->find($id);
        $oldStatus = $post->status;

        $post->update([
            'status'                 => PostStatus::APPROVED,
            'change_request_comment' => null,
        ]);

        $this->logAction(
            $post,
            'approved',
            'Post approved',
            ['status' => $oldStatus],
            ['status' => PostStatus::APPROVED]
        );

        return $post;
    }

    public function changeRequest($id, $comment)
    {
        $post      = $this->find($id);
        $oldValues = [
            'status'                 => $post->status,
            'change_request_comment' => $post->change_request_comment,
        ];

        $post->update([
            'status'                 => PostStatus::CHANGE_REQUEST,
            'change_request_comment' => $comment,
        ]);

        $this->logAction(
            $post,
            'change_requested',
            'Change requested: ' . $comment,
            $oldValues,
            [
                'status'                 => PostStatus::CHANGE_REQUEST,
                'change_request_comment' => $comment,
            ]
        );

        return $post;
    }

    public function publish($id)
    {
        $post = $this->find($id);

        if ($post->status !== PostStatus::APPROVED) {
            throw new \Exception('Post must be approved before publishing');
        }

        $oldValues = [
            'type'         => $post->type,
            'status'       => $post->status,
            'published_at' => $post->published_at,
        ];

        $post->update([
            'type'         => PostType::PUBLISHED,
            'status'       => PostStatus::PUBLISHED,
            'published_at' => now(),
        ]);

        $this->logAction(
            $post,
            'published',
            'Post published',
            $oldValues,
            [
                'type'         => PostType::PUBLISHED,
                'status'       => PostStatus::PUBLISHED,
                'published_at' => now(),
            ]
        );

        return $post;
    }

    public function getFiltered(array $filters)
    {
        $query = $this->model->with(['client.user', 'platforms']);

        // if (isset($filters['month']) && isset($filters['year'])) {
        //     $query->whereMonth('scheduled_at', $filters['month'])
        //         ->whereYear('scheduled_at', $filters['year']);
        // }
        if (isset($filters['month'])) {
            $year = $filters['year'] ?? now()->year;
            $query->whereMonth('scheduled_at', $filters['month'])
                ->whereYear('scheduled_at', $year);
        }
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['platform'])) {
            $query->whereHas('platforms', function ($query) use ($filters) {
                $query->where('platform', $filters['platform']);
            });
        }

        if (isset($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        return $query->orderBy('updated_at', 'desc')->orderBy('scheduled_at', 'desc')->paginate();
    }

    public function checkExpiredPosts()
    {
        $now = Carbon::now();

        // Get all scheduled posts that are past their scheduled time and are not published
        $expiredPosts = $this->model
            ->where('type', PostType::UPCOMING)
            ->where('scheduled_at', '<', $now)
            ->where('status', '!=', PostStatus::PUBLISHED)
            ->get();

        foreach ($expiredPosts as $post) {
            $oldValues = [
                'type'         => $post->type,
                'status'       => $post->status,
                'published_at' => $post->published_at,
            ];

            $post->update([
                'type' => PostType::ENDED,
                //'status' => PostStatus::PENDING,
                // 'published_at' => $now
            ]);

            $this->logAction(
                $post,
                'auto_ended',
                'Post automatically ended by system after reaching scheduled time',
                $oldValues,
                [
                    'type'   => PostType::ENDED,
                    // 'status' => PostStatus::PENDING,
                    // 'published_at' => $now
                ],
                userId: null // System action, no user_id
            );
        }

        return $expiredPosts->count();
    }

    protected function uploadImage($base64Image)
    {
        // Extract the image data from base64 string
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));

        // Generate unique filename
        $filename = 'posts/' . uniqid() . '.jpg';

        // Store the image
        Storage::disk('public')->put($filename, $imageData);

        return $filename;
    }

    public function getPostById($id)
    {
        return $this->model
            ->with(['client.user', 'platforms','mediaPhotos','mediaVideos'])
            ->findOrFail($id);
    }

    public function getFilteredPosts(array $filters, $perPage = 10)
    {
        $query = $this->model->with(['client.user', 'platforms','mediaPhotos','mediaVideos']);

        // If the current user is an account manager, filter by their assigned clients
        $user = request()->user();
        if ($user && $user->hasRole('account_manager')) {
            // Get the team member record for this account manager
            $teamMember = $user->teamMember;
            if ($teamMember) {
                // Use the assigned_clients array if it exists and has values
                if (! empty($teamMember->assigned_clients)) {
                    $query->whereIn('client_id', $teamMember->assigned_clients);
                } else {
                    // Otherwise use the clients relationship if they have any assigned
                    $assignedClientIds = $teamMember->clients()->pluck('clients.id')->toArray();
                    if (! empty($assignedClientIds)) {
                        $query->whereIn('client_id', $assignedClientIds);
                    }
                }
            }
        }

        // Filter by month and year
        if (isset($filters['month']) && isset($filters['year'])) {
            $query->whereMonth('scheduled_at', $filters['month'])
                ->whereYear('scheduled_at', $filters['year']);
        }

        // Filter by status
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Filter by single platform
        if (isset($filters['platform'])) {
            $query->whereHas('platforms', function ($query) use ($filters) {
                $query->where('platform', $filters['platform']);
            });
        }

        // Filter by multiple platforms
        if (isset($filters['platforms'])) {
            $platformArray = explode(',', $filters['platforms']);
            if (! empty($platformArray)) {
                $query->whereHas('platforms', function ($query) use ($platformArray) {
                    $query->whereIn('platform', $platformArray);
                });
            }
        }

        // Filter by single client
        if (isset($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        // Filter by multiple clients
        if (isset($filters['client_ids'])) {
            $clientIds = explode(',', $filters['client_ids']);
            if (! empty($clientIds)) {
                $query->whereIn('client_id', $clientIds);
            }
        }

        // Search by title
        if (isset($filters['title_search']) && ! empty($filters['title_search'])) {
            $query->where('title', 'like', '%' . $filters['title_search'] . '%');
        }

        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'updated_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        return $query->paginate($perPage);
    }
}
