<?php

namespace Database\Seeders;

use App\Enums\NotificationType;
use App\Models\Notification;
use App\Models\Post;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    public function run(): void
    {
        // Get all posts that have clients
        $posts = Post::whereHas('client.user')->get();
        
        // Get admin users (super_admin and account_manager)
        $adminUsers = User::role(['super_admin', 'account_manager'])->get();
        
        if ($posts->isEmpty() || $adminUsers->isEmpty()) {
            return;
        }

        // Create notifications for the last 30 days
        $startDate = now()->subDays(30);
        $types = NotificationType::cases();

        // Generate 50 notifications
        for ($i = 0; $i < 50; $i++) {
            $post = $posts->random();
            $adminUser = $adminUsers->random();
            $type = $types[array_rand($types)];
            
            // Random date within the last 30 days
            $createdAt = Carbon::instance($startDate)->addMinutes(rand(0, 43200)); // 43200 minutes = 30 days

            // Random read status with more unread for recent notifications
            $isRead = $createdAt->lt(now()->subDays(15)) ? (bool)rand(0, 1) : false;

            Notification::create([
                'user_id' => $post->client->user_id,
                'admin_user_id' => $adminUser->id,
                'post_id' => $post->id,
                'message' => $type->getMessage($post->title, $adminUser->name),
                'action_type' => $type->value,
                'is_read' => $isRead,
                'created_at' => $createdAt,
                'updated_at' => $isRead ? $createdAt->copy()->addHours(rand(1, 48)) : $createdAt,
            ]);
        }

        // Create some specific scenario notifications
        $this->createScenarioNotifications($posts, $adminUsers);
    }

    private function createScenarioNotifications($posts, $adminUsers): void
    {
        // Scenario 1: Post lifecycle notifications
        $post = $posts->random();
        $adminUser = $adminUsers->random();
        $baseDate = now()->subDays(5);

        // Create sequence of notifications for the same post
        $scenarios = [
            [
                'type' => NotificationType::POST_CREATED,
                'hours_offset' => 0,
                'is_read' => true
            ],
            [
                'type' => NotificationType::POST_PREVIEW_SENT,
                'hours_offset' => 2,
                'is_read' => true
            ],
            [
                'type' => NotificationType::POST_CHANGE_REQUESTED,
                'hours_offset' => 4,
                'is_read' => true
            ],
            [
                'type' => NotificationType::POST_UPDATED,
                'hours_offset' => 24,
                'is_read' => true
            ],
            [
                'type' => NotificationType::POST_APPROVED,
                'hours_offset' => 26,
                'is_read' => false
            ],
            [
                'type' => NotificationType::POST_PUBLISHED,
                'hours_offset' => 48,
                'is_read' => false
            ],
        ];

        foreach ($scenarios as $scenario) {
            $createdAt = $baseDate->copy()->addHours($scenario['hours_offset']);
            
            Notification::create([
                'user_id' => $post->client->user_id,
                'admin_user_id' => $adminUser->id,
                'post_id' => $post->id,
                'message' => $scenario['type']->getMessage($post->title, $adminUser->name),
                'action_type' => $scenario['type']->value,
                'is_read' => $scenario['is_read'],
                'created_at' => $createdAt,
                'updated_at' => $scenario['is_read'] ? $createdAt->copy()->addHours(1) : $createdAt,
            ]);
        }

        // Scenario 2: Multiple notifications for different posts on the same day
        $todayPosts = $posts->random(3);
        foreach ($todayPosts as $post) {
            Notification::create([
                'user_id' => $post->client->user_id,
                'admin_user_id' => $adminUsers->random()->id,
                'post_id' => $post->id,
                'message' => NotificationType::POST_CREATED->getMessage($post->title, $adminUser->name),
                'action_type' => NotificationType::POST_CREATED->value,
                'is_read' => false,
                'created_at' => now()->subHours(rand(1, 8)),
            ]);
        }
    }
}