<?php

namespace App\Enums;

enum Platform: string
{
    case SEO = 'seo';
    case FACEBOOK = 'facebook';
    case INSTAGRAM = 'instagram';
    case TWITTER = 'twitter';
    case LINKEDIN = 'linkedin';
    case TIKTOK = 'tiktok';
    case SNAPCHAT = 'snapchat';
    case YOUTUBE = 'youtube';

    public function label(): string
    {
        return match($this) {
            self::SEO => 'SEO',
            self::FACEBOOK => 'Facebook',
            self::INSTAGRAM => 'Instagram',
            self::TWITTER => 'Twitter',
            self::LINKEDIN => 'LinkedIn',
            self::TIKTOK => 'TikTok',
            self::SNAPCHAT => 'Snapchat',
            self::YOUTUBE => 'YouTube',
        };
    }

    public static function options(): array
    {
        return collect(self::cases())->map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label()
        ])->toArray();
    }
}

