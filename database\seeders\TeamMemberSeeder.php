<?php
namespace Database\Seeders;

use App\Models\Client;
use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class TeamMemberSeeder extends Seeder
{
    public function run(): void
    {
        $teamMembers = [
            [
                'name'       => 'Account Manager',
                'email'      => '<EMAIL>',
                'role'       => 'account_manager',
                'password'   => 'M@n4g3r#2024!XyZ',
                'whatsapp'   => '+**********',
                'start_time' => '09:00',
                'end_time'   => '17:00',
                'work_days'  => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            ],
            [
                'name'       => 'Content Creator',
                'email'      => '<EMAIL>',
                'role'       => 'content_creator',
                'password'   => 'Cr3@t0r$2024%AbC',
                'whatsapp'   => '+**********',
                'start_time' => '09:00',
                'end_time'   => '17:00',
                'work_days'  => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            ],
            [
                'name'       => 'Social Media Specialist',
                'email'      => '<EMAIL>',
                'role'       => 'social_media_specialist',
                'password'   => 'S0c!@l#2024&PqR',
                'whatsapp'   => '+1234567892',
                'start_time' => '10:00',
                'end_time'   => '18:00',
                'work_days'  => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            ],
            [
                'name'       => 'Graphic Designer',
                'email'      => '<EMAIL>',
                'role'       => 'graphic_designer',
                'password'   => 'D3s!gn3r*2024^LmN',
                'whatsapp'   => '+1234567893',
                'start_time' => '08:00',
                'end_time'   => '16:00',
                'work_days'  => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
            ],
            [
                'name'       => 'Content Strategist',
                'email'      => '<EMAIL>',
                'role'       => 'content_strategist',
                'password'   => 'Str@t3g!st#2024&DeF',
                'whatsapp'   => '+1234567894',
                'start_time' => '11:00',
                'end_time'   => '19:00',
                'work_days'  => ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'],
            ],
        ];

        $clients = Client::all();

        foreach ($teamMembers as $member) {
            // Create user
            $user = User::firstOrCreate(
                ['email' => $member['email']],
                [
                    'name'               => $member['name'],
                    'password'           => Hash::make($member['password']),
                    'profile_image_path' => $this->generateProfileImage(),
                ]
            );

            // Store the password in a log file for development purposes
            // $this->logPassword($member['email'], $member['password'], $member['role']);

            // Assign role
            $user->assignRole($member['role']);

            // Create team member
            $teamMember = TeamMember::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'start_time'      => $member['start_time'],
                    'end_time'        => $member['end_time'],
                    'whatsapp_number' => $member['whatsapp'],
                    'chat_disabled'   => $member['role'] !== 'account_manager' ? true : false,
                    'work_days'       => $member['work_days'],
                ]
            );

            // Randomly assign 1-2 clients to each team member
            $randomClients = $clients->random(rand(1, 2));
            $teamMember->clients()->sync($randomClients->pluck('id'));
        }
    }

    protected function generateProfileImage(): ?string
    {
        try {
            // Generate a square image for profile
            $size     = 400; // 400x400 pixels
            $imageUrl = "https://picsum.photos/{$size}/{$size}";

            $response = Http::get($imageUrl);
            if ($response->successful()) {
                $filename = 'profiles/' . uniqid() . '.jpg';
                Storage::disk('public')->put($filename, $response->body());
                return $filename;
            }
        } catch (\Exception $e) {
            report($e);
        }

        return null;
    }

    // protected function logPassword(string $email, string $password, string $role): void
    // {
    //     $logPath = storage_path('logs/seeder_passwords.log');
    //     $content = "[" . now() . "] {$role}: {$email} - {$password}" . PHP_EOL;
    //     file_put_contents($logPath, $content, FILE_APPEND);
    // }
}