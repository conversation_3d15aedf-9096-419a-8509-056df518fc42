<?php

namespace App\Http\Requests\SenderEmail;

use App\Http\Requests\BaseRequest;

class StoreSenderEmailRequest extends BaseRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasRole('super_admin');
    }

    public function rules(): array
    {
        return [
            'email' => 'required|email|unique:sender_emails,email',
            'is_default' => 'boolean'
        ];
    }
}