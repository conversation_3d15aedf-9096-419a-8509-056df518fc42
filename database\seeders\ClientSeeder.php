<?php
namespace Database\Seeders;

use App\Models\User;
use App\Models\Client;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class ClientSeeder extends Seeder
{
    public function run(): void
    {
        // Ensure the profiles directory exists
        Storage::disk('public')->makeDirectory('profiles');

        // Create first client with user
        $password1 = 'Cl!3nt1#2024$GhI';
        $user1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Client User One',
                'password' => Hash::make($password1),
                'profile_image_path' => $this->generateProfileImage()
            ]
        );
        $user1->assignRole('client');
        // $this->logPassword('<EMAIL>', $password1, 'client');

        Client::firstOrCreate(
            ['user_id' => $user1->id],
            [
                'phone' => '+1234567890',
                'address' => '123 Business Street, Suite 100',
                'is_active' => true,
            ]
        );

        // Create second client with user
        $password2 = 'Cl!3nt2#2024$JkL';
        $user2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Client User Two',
                'password' => Hash::make($password2),
                'profile_image_path' => $this->generateProfileImage()
            ]
        );
        $user2->assignRole('client');
        // $this->logPassword('<EMAIL>', $password2, 'client');

        Client::firstOrCreate(
            ['user_id' => $user2->id],
            [
                'phone' => '+1987654321',
                'address' => '456 Corporate Avenue, Floor 2',
                'is_active' => true,
            ]
        );
    }

    protected function generateProfileImage(): ?string
    {
        try {
            // Generate a square image for profile
            $size = 400; // 400x400 pixels
            $imageUrl = "https://picsum.photos/{$size}/{$size}";

            $response = Http::get($imageUrl);
            if ($response->successful()) {
                $filename = 'profiles/' . uniqid() . '.jpg';
                Storage::disk('public')->put($filename, $response->body());
                return $filename;
            }
        } catch (\Exception $e) {
            report($e);
        }

        return null;
    }

    // protected function logPassword(string $email, string $password, string $role): void
    // {
    //     $logPath = storage_path('logs/seeder_passwords.log');
    //     $content = "[" . now() . "] {$role}: {$email} - {$password}" . PHP_EOL;
    //     file_put_contents($logPath, $content, FILE_APPEND);
    // }
}




